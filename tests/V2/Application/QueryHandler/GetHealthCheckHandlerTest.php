<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler;

use App\V2\Application\Query\GetHealthCheck;
use App\V2\Application\QueryHandler\GetHealthCheckHandler;
use App\V2\Domain\Shared\Email\Email;
use App\V2\Domain\User\UserCriteria;
use App\V2\Domain\User\UserRepository;
use PHPUnit\Framework\TestCase;

class GetHealthCheckHandlerTest extends TestCase
{
    private UserRepository $userRepository;
    private GetHealthCheckHandler $handler;

    protected function setUp(): void
    {
        $this->userRepository = $this->createMock(UserRepository::class);
        $this->handler = new GetHealthCheckHandler($this->userRepository);
    }

    public function testHandleCallsRepositoryCountBy(): void
    {
        // Arrange
        $email = new Email('<EMAIL>');
        $criteria = UserCriteria::createEmpty()->filterByEmail($email);
        $query = new GetHealthCheck($criteria);

        $this->userRepository
            ->expects($this->once())
            ->method('countBy')
            ->with($criteria)
            ->willReturn(1);

        // Act
        $this->handler->handle($query);

        // Assert - The expectation is verified by PHPUnit
    }

    public function testHandleWorksWhenUserNotFound(): void
    {
        // Arrange
        $email = new Email('<EMAIL>');
        $criteria = UserCriteria::createEmpty()->filterByEmail($email);
        $query = new GetHealthCheck($criteria);

        $this->userRepository
            ->expects($this->once())
            ->method('countBy')
            ->with($criteria)
            ->willReturn(0);

        // Act
        $this->handler->handle($query);

        // Assert - The expectation is verified by PHPUnit
    }

    public function testHandleWorksWhenDatabaseThrowsException(): void
    {
        // Arrange
        $email = new Email('<EMAIL>');
        $criteria = UserCriteria::createEmpty()->filterByEmail($email);
        $query = new GetHealthCheck($criteria);

        $this->userRepository
            ->expects($this->once())
            ->method('countBy')
            ->with($criteria)
            ->willThrowException(new \Exception('Database connection failed'));

        // Act & Assert
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Database connection failed');
        
        $this->handler->handle($query);
    }
}
