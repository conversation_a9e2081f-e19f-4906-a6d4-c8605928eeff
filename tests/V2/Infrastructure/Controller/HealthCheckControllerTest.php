<?php

declare(strict_types=1);

namespace App\Tests\V2\Infrastructure\Controller;

use App\V2\Domain\User\UserRepository;
use Symfony\Bundle\FrameworkBundle\Test\WebTestCase;
use Symfony\Component\HttpFoundation\Response;

class HealthCheckControllerTest extends WebTestCase
{
    public function testHealthCheckReturns204WhenDatabaseIsHealthy(): void
    {
        // Arrange
        $client = static::createClient();

        // Act
        $client->request('GET', '/api/v2/health');

        // Assert
        $this->assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
        $this->assertEmpty($client->getResponse()->getContent());
    }

    public function testHealthCheckReturns500WhenDatabaseFails(): void
    {
        // Arrange
        $client = static::createClient();
        
        // Mock the UserRepository to throw an exception
        $userRepository = $this->createMock(UserRepository::class);
        $userRepository
            ->method('countBy')
            ->willThrowException(new \Exception('Database connection failed'));

        // Replace the service in the container
        static::getContainer()->set(UserRepository::class, $userRepository);

        // Act
        $client->request('GET', '/api/v2/health');

        // Assert
        $this->assertResponseStatusCodeSame(Response::HTTP_INTERNAL_SERVER_ERROR);
    }

    public function testHealthCheckIsPubliclyAccessible(): void
    {
        // Arrange
        $client = static::createClient();

        // Act - Make request without authentication
        $client->request('GET', '/api/v2/health');

        // Assert - Should not return 401 Unauthorized
        $this->assertNotSame(Response::HTTP_UNAUTHORIZED, $client->getResponse()->getStatusCode());
    }

    public function testHealthCheckSearchesForSupportUser(): void
    {
        // Arrange
        $client = static::createClient();
        
        // Mock the UserRepository to verify the correct email is searched
        $userRepository = $this->createMock(UserRepository::class);
        $userRepository
            ->expects($this->once())
            ->method('countBy')
            ->with($this->callback(function ($criteria) {
                return $criteria->getEmail() !== null 
                    && $criteria->getEmail()->value() === '<EMAIL>';
            }))
            ->willReturn(1);

        // Replace the service in the container
        static::getContainer()->set(UserRepository::class, $userRepository);

        // Act
        $client->request('GET', '/api/v2/health');

        // Assert
        $this->assertResponseStatusCodeSame(Response::HTTP_NO_CONTENT);
    }
}
