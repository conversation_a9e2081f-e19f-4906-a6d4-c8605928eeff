<?php

declare(strict_types=1);

namespace App\V2\Application\QueryHandler;

use App\V2\Application\Query\GetHealthCheck;
use App\V2\Domain\User\UserRepository;

readonly class GetHealthCheckHandler
{
    public function __construct(
        private UserRepository $userRepository
    ) {
    }

    public function handle(GetHealthCheck $query): void
    {
        $criteria = $query->getCriteria();

        // Perform a database query to check connectivity
        // This serves as a health check - we don't care about the result,
        // just that the database connection works
        $this->userRepository->countBy($criteria);
    }
}
